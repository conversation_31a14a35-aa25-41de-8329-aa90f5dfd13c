package postgres

import (
	"fmt"
	"log"
	"ops-api/config"
	"os"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// Connect creates a PostgreSQL database connection
func Connect(cfg *config.Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=require",
		cfg.DBHost, cfg.DBUser, cfg.DBPassword, cfg.DBName, cfg.DBPort)
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			//SlowThreshold:             time.Second, // Slow SQL threshold
			//LogLevel:                  logger.Info, // Log level
			IgnoreRecordNotFoundError: true, // Ignore ErrRecordNotFound
			Colorful:                  true, // Colored output
		},
	)
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: newLogger,
		NamingStrategy: schema.NamingStrategy{
			// TablePrefix:   "t_", // table name prefix, table for `User` would be `t_users`
			SingularTable: true,  // use singular table name, table for `User` would be `user` with this option enabled
			NoLowerCase:   false, // skip the snake_casing of names
			// NameReplacer:  strings.NewReplacer("CID", "Cid"), // use name replacer to change struct/field name before convert it to db name
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
	}

	// Configure connection pool settings
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Set maximum number of open connections to the database
	sqlDB.SetMaxOpenConns(25)

	// Set maximum number of idle connections in the pool
	sqlDB.SetMaxIdleConns(5)

	// Set maximum amount of time a connection may be reused
	sqlDB.SetConnMaxLifetime(5 * time.Minute) // 5 minutes

	return db, nil
}
