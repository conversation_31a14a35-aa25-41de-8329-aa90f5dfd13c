terraform {
  # backend "pg" {
  #   conn_str             = ""
  #   schema_name          = ""
  #   skip_schema_creation = false
  # }
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    http = {
      source  = "hashicorp/http"
      version = "3.4.3"
    }
    # local = {
    #   source  = "hashicorp/local"
    #   version = "2.5.1"
    # }
    # kubectl = {
    #   source  = "gavin<PERSON>nney/kubectl"
    #   version = ">= 1.7.0"
    # }
  }
}

provider "digitalocean" {
  token = var.do_token
}


